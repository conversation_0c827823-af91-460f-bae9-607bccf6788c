# Dependencies
node_modules/
python/venv/
venv/
env/

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
python/__pycache__/
src/__pycache__/
*.pyc
*.pyo
*.pyd

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Streamlit secrets (for deployment)
.streamlit/secrets.toml

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Large model files are now tracked with Git LFS
# python/models/*.safetensors  # Now tracked with LFS
# python/models/*.zip          # Now tracked with LFS
# python/models/*.bin          # Now tracked with LFS

# Other large model files (if not using LFS)
python/models/*.h5
python/models/*.pkl
python/models/*.joblib

# Model cache (optional - uncomment if you want to ignore model cache)
# python/models/.cache/

# Build outputs
dist/
build/
