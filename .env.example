# Facebook API Configuration
PAGE_ID=your_facebook_page_id
PAGE_ACCESS_TOKEN=your_facebook_page_access_token

# Model Configuration
MODEL_PATH=./src/models
CONFIDENCE_THRESHOLD=0.5

# Auto Monitor Configuration
MONITOR_POLL_INTERVAL=30000
AUTO_DELETE_SPAM=true

# Spam Detector Configuration
SPAM_DETECTOR_MODE=child_process
SPAM_DETECTOR_API_URL=http://localhost:5000
SPAM_DETECTOR_TIMEOUT=30000

# Optional: Gemini API Key (if used)
GEMINI_API_KEY=your_gemini_api_key_if_needed

# Python Configuration (optional)
PYTHON_PATH=python

# Debug Configuration
DEBUG=false
